﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Guards;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields
{
    public sealed class MultiSelectOptionFieldValue : CreativeFieldValue<IReadOnlyList<long>>
    {
        public MultiSelectOptionFieldValue (CreativeFieldIdentifier creativeFieldIdentifier, IEnumerable<long>? value)
            : base(creativeFieldIdentifier, value?.ToList() ?? new List<long>())
        {
        }

        internal static Result<MultiSelectOptionFieldValue> Create (
            CreativeFieldIdentifier creativeFieldIdentifier, object? value,
            IEnumerable<ValidationRule>? validationRules)
        {
            Guard.Argument(creativeFieldIdentifier, nameof(creativeFieldIdentifier))
                .FieldTypeIs(CreativeFieldType.MultiSelectOption);

            if (value is not null)
            {
                Guard.Argument(value, nameof(value)).Compatible<IEnumerable<long>>();
            }

            Result validationResults =
                CreativeFieldValueValidationManager.Validate((IEnumerable<long>?)value, validationRules,
                    creativeFieldIdentifier);

            return validationResults.IsFailed
                ? validationResults
                : new MultiSelectOptionFieldValue(creativeFieldIdentifier, (IEnumerable<long>?)value);
        }

        public override Result<CreativeFieldValue> GenerateNewValue (object? value,
            IEnumerable<ValidationRule> validationRules,
            IReadOnlyDictionary<CreativeFileId, CreativeFile>? creativeFiles = null)
        {
            return Create(CreativeFieldIdentifier, value, validationRules)
                .ToResult<CreativeFieldValue>(creativeFieldValue => creativeFieldValue);
        }
    }
}
﻿using AutoMapper;
using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Microsoft.Azure.Cosmos;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.Enums;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using FluentResults;
using Microsoft.Azure.Cosmos.Linq;
using Microsoft.Extensions.Options;
using System.Net;
using Fattail.CreativeManagement.API.Application.CreativeFiles.CreativeFilesUpload;
using Microsoft.Extensions.Logging;
using Fattail.CreativeManagement.API.Domain.CreativeFiles.Types;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Repositories;

internal sealed class CreativeFileRepository (
    ICosmosDbContainerFactory cosmosDbContainerFactory,
    IMapper mapper,
    IOrganizationContext organizationContext,
    IOptions<ParallelExecutionSettings> parallelExecutionSettings,
    ICreativeFileStorageManager creativeFileStorageManager,
    ILogger<CreativeFileRepository> log)
    : CosmosDbRepository<CreativeFile, CreativeFileId, Entities.CreativeFile>(cosmosDbContainerFactory, mapper,
            organizationContext, parallelExecutionSettings, log),
        ICreativeFileRepository
{
    public override string ContainerName => "CreativeFiles";

    public override PartitionKey ResolvePartitionKey ()
    {
        return new PartitionKey($"{_organizationContext.OrganizationId}");
    }

    public async Task<Result> DeletePartitionAsync ()
    {
        PartitionKey partitionKey = ResolvePartitionKey();
        ResponseMessage? result = await _container.DeleteAllItemsByPartitionKeyStreamAsync(partitionKey);
        return result.StatusCode == HttpStatusCode.OK ? Result.Ok() : Result.Fail(partitionKey.ToString());
    }

    public async Task<IReadOnlyList<TResult>> FindMigratedAsync<TResult> ()
    {
        var items = new List<TResult>();
        IQueryable<Entities.CreativeFile> creativeFilesDbQuery = _container
            .GetItemLinqQueryable<Entities.CreativeFile>(
                requestOptions: new QueryRequestOptions { PartitionKey = ResolvePartitionKey() },
                linqSerializerOptions: new CosmosLinqSerializerOptions()
                {
                    PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase
                })
            .Where(c => c.LastAction == ActionType.Migrate);

        using (var resultSet = creativeFilesDbQuery.ToFeedIterator())
        {
            while (resultSet.HasMoreResults)
            {
                FeedResponse<Entities.CreativeFile>? response = await _retryPolicy.ExecuteAsync(() => resultSet.ReadNextAsync());
                items.AddRange(_mapper.Map<IReadOnlyList<TResult>>(response.Resource));
            }
        }
        return items;
    }

    public override async Task<CreativeFile?> FindByIdAsync (CreativeFileId id)
    {
        try
        {
            ItemResponse<Entities.CreativeFile> response = await _retryPolicy.ExecuteAsync(() =>
                _container.ReadItemAsync<Entities.CreativeFile>(id?.ToString(), ResolvePartitionKey()));

            Entities.CreativeFile cosmosEntity = response.Resource;

            await SelfHealCreativeFileIfNeeded(cosmosEntity);

            return _mapper.Map<CreativeFile>(cosmosEntity);
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            return null;
        }
    }

    public override async Task<IReadOnlyList<TResult>> FindManyByIdAsync<TResult> (IEnumerable<CreativeFileId> ids)
    {
        try
        {
            IEnumerable<Entities.CreativeFile> cosmosCreativeFiles = await FindManyInCosmosByIdAsync<Entities.CreativeFile>(ids);

            foreach (Entities.CreativeFile cosmosCreativeFile in cosmosCreativeFiles)
            {
                await SelfHealCreativeFileIfNeeded(cosmosCreativeFile);
            }

            return _mapper.Map<IReadOnlyList<TResult>>(cosmosCreativeFiles);
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            return [];
        }
    }

    private async Task SelfHealCreativeFileIfNeeded (Entities.CreativeFile cosmosEntity)
    {
        bool needsUpdate = false;

        needsUpdate |= await EnrichSizeIfNeeded(cosmosEntity);
        needsUpdate |= EnrichTypeIfNeeded(cosmosEntity);
        needsUpdate |= await EnrichMetadataIfNeeded(cosmosEntity);

        if (needsUpdate)
        {
            await UpdateCosmosEntityAsync(cosmosEntity);
        }
    }

    private async Task<bool> EnrichSizeIfNeeded (Entities.CreativeFile cosmosEntity)
    {
        if (cosmosEntity.Size == 0 && !string.IsNullOrEmpty(cosmosEntity.BlobName))
        {
            long sizeInBytes = await creativeFileStorageManager.GetFileSizeInBytesAsync(cosmosEntity.BlobName);

            if (sizeInBytes > 0)
            {
                cosmosEntity.Size = sizeInBytes;
                return true;
            }
        }
        return false;
    }

    private static bool EnrichTypeIfNeeded (Entities.CreativeFile cosmosEntity)
    {
        if (cosmosEntity.Type == CreativeFileTypeEnum.Other && !string.IsNullOrEmpty(cosmosEntity.Extension))
        {
            var fileExtension = FileExtension.From(cosmosEntity.Extension);
            var creativeFileType = CreativeFileType.FromExtension(fileExtension);

            if (creativeFileType.EnumType != CreativeFileTypeEnum.Other)
            {
                cosmosEntity.Type = creativeFileType.EnumType;
                return true;
            }
        }
        return false;
    }

    private async Task<bool> EnrichMetadataIfNeeded (Entities.CreativeFile cosmosEntity)
    {
        if ((cosmosEntity.Metadata == null || cosmosEntity.Metadata.Count == 0) &&
            cosmosEntity.Type == CreativeFileTypeEnum.Image &&
            !string.IsNullOrEmpty(cosmosEntity.BlobName))
        {
            try
            {
                using Stream? fileStream = await creativeFileStorageManager.GetFileStreamAsync(cosmosEntity.BlobName);
                if (fileStream != null)
                {
                    CreativeFileType creativeFileType = CreativeFileType.Image;
                    CreativeFileMetadata metadata = creativeFileType.GenerateMetadata(fileStream);
                    cosmosEntity.Metadata = metadata;
                    return true;
                }
            }
            catch (Exception ex)
            {
                log.LogWarning(ex, "Failed to generate metadata for creative file {CreativeFileId} with blob {BlobName}",
                    cosmosEntity.Id, cosmosEntity.BlobName);
            }
        }
        return false;
    }
}
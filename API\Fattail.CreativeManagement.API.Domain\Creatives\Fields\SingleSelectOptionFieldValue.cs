﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Guards;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields;

public sealed class SingleSelectOptionFieldValue : CreativeFieldValue<long?>
{
    public SingleSelectOptionFieldValue (CreativeFieldIdentifier creativeFieldIdentifier, long? value)
        : base(creativeFieldIdentifier, value)
    {
    }

    internal static Result<SingleSelectOptionFieldValue> Create (
        CreativeFieldIdentifier creativeFieldIdentifier, object? value,
        IEnumerable<ValidationRule>? validationRules)
    {
        Guard.Argument(creativeFieldIdentifier, nameof(creativeFieldIdentifier))
            .FieldTypeIs(CreativeFieldType.SingleSelectOption);

        if (value is not null)
        {
            if (!long.TryParse(value.ToString(), out long fieldValue))
            {
                return Result.Fail(
                    new InvalidValueError(
                        $"The value '{value}' is not a valid long integer.",
                        nameof(SingleSelectOptionFieldValue)));
            }
            value = fieldValue;
        }

        Result validationResults =
            CreativeFieldValueValidationManager.Validate((long?)value, validationRules,
                creativeFieldIdentifier);

        return validationResults.IsFailed
            ? validationResults
            : new SingleSelectOptionFieldValue(creativeFieldIdentifier, (long?)value);
    }

    public override Result<CreativeFieldValue> GenerateNewValue (object? value,
        IEnumerable<ValidationRule> validationRules,
        IReadOnlyDictionary<CreativeFileId, CreativeFile>? creativeFiles = null)
    {
        return Create(CreativeFieldIdentifier, value, validationRules)
            .ToResult<CreativeFieldValue>(creativeFieldValue => creativeFieldValue);
    }
}
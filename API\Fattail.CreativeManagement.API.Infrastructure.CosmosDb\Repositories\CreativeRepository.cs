﻿using AutoMapper;
using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using FluentResults;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Linq;
using System.Net;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFiles.Errors;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.Enums;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Creative = Fattail.CreativeManagement.API.Domain.Creatives.Creative;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Repositories;

internal sealed class CreativeRepository : CosmosDbRepository<Creative, long, Entities.Creative>,
    ICreativeRepository
{
    public CreativeRepository (
        ICosmosDbContainerFactory cosmosDbContainerFactory,
        IMapper mapper,
        IOrganizationContext organizationContext,
        IOptions<ParallelExecutionSettings> parallelExecutionSettings,
        ILogger<CreativeRepository> log) : base(cosmosDbContainerFactory, mapper,
        organizationContext, parallelExecutionSettings, log)
    {
    }

    public override string ContainerName => "Creatives";

    public override PartitionKey ResolvePartitionKey ()
    {
        return new PartitionKey($"{_organizationContext.OrganizationId}");
    }

    public async Task<IReadOnlyList<TResult>> FindManyByCreativeTemplateIdAsync<TResult> (CreativeTemplateId id)
    {
        var items = new List<TResult>();
        try
        {
            IQueryable<Entities.Creative> creativeDbQuery = _container
                .GetItemLinqQueryable<Entities.Creative>(
                    requestOptions: new QueryRequestOptions { PartitionKey = ResolvePartitionKey() },
                    linqSerializerOptions: new CosmosLinqSerializerOptions()
                    {
                        PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase
                    })
                .Where(c => c.CreativeTemplateId == id.ToString());

            using (var resultSet = creativeDbQuery.ToFeedIterator())
            {
                while (resultSet.HasMoreResults)
                {
                    FeedResponse<Entities.Creative>? response = await resultSet.ReadNextAsync();
                    items.AddRange(_mapper.Map<IReadOnlyList<TResult>>(response.Resource));
                }
            }

            return items;
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            return default;
        }
    }

    public async Task<Result<Creative>> FindMigratedAdBookCreativeAsync (long adBookClientId, long adBookCampaignId, long adBookAdId)
    {
        try
        {
            IQueryable<Entities.Creative> creativeDbQuery = _container
                .GetItemLinqQueryable<Entities.Creative>(
                    requestOptions: new QueryRequestOptions { PartitionKey = ResolvePartitionKey() },
                    linqSerializerOptions: new CosmosLinqSerializerOptions()
                    {
                        PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase
                    })
                .Where(c =>
                    c.AdBookAdId == adBookAdId.ToString() &&
                    c.AdBookClientId == adBookClientId.ToString() &&
                    c.CampaignId == adBookCampaignId.ToString());

            using (var resultSet = creativeDbQuery.ToFeedIterator())
            {
                while (resultSet.HasMoreResults)
                {
                    FeedResponse<Entities.Creative>? response =
                        await _retryPolicy.ExecuteAsync(() => resultSet.ReadNextAsync());
                    Entities.Creative? creative = response.Resource.FirstOrDefault();

                    if (creative != null)
                    {
                        if (creative.LastAction == ActionType.Update)
                        {
                            return Result.Fail(new MigratedCreativeFileIsUpdatedError(adBookAdId, adBookClientId,
                                adBookCampaignId));
                        }

                        return _mapper.Map<Creative>(creative);
                    }
                }
            }

            return Result.Fail(new EntityNotFoundError(adBookAdId.ToString(), nameof(CreativeFile)));
        }
        catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.NotFound)
        {
            return Result.Fail(new EntityNotFoundError(adBookAdId.ToString(), nameof(CreativeFile)));
        }
    }

    public async Task<Result> DeletePartitionAsync ()
    {
        PartitionKey partitionKey = ResolvePartitionKey();
        ResponseMessage? result = await _container.DeleteAllItemsByPartitionKeyStreamAsync(partitionKey);
        return result.StatusCode == HttpStatusCode.OK ? Result.Ok() : Result.Fail(partitionKey.ToString());
    }

    public async Task<Result> DeleteMigratedAsync ()
    {
        var result = new Result();
        IQueryable<Entities.Creative> creativeDbQuery = _container
            .GetItemLinqQueryable<Entities.Creative>(
                requestOptions: new QueryRequestOptions { PartitionKey = ResolvePartitionKey() },
                linqSerializerOptions: new CosmosLinqSerializerOptions()
                {
                    PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase
                })
            .Where(c => c.LastAction == ActionType.Migrate);

        using (var resultSet = creativeDbQuery.ToFeedIterator())
        {
            while (resultSet.HasMoreResults)
            {
                FeedResponse<Entities.Creative>? response = await _retryPolicy.ExecuteAsync(() => resultSet.ReadNextAsync());
                Parallel.ForEach(response.Resource,
                    new ParallelOptions
                    {
                        MaxDegreeOfParallelism = _parallelExecutionSettings.MaxDegreeOfParallelismForDeletion
                    }, async creative =>
                    {
                        ItemResponse<Entities.Creative>? res = await _retryPolicy.ExecuteAsync(() =>
                            _container.DeleteItemAsync<Entities.Creative>(creative.Id, ResolvePartitionKey()));

                        if (res.StatusCode != HttpStatusCode.NoContent)
                        {
                            lock (result)
                            {
                                Result.Merge(result, Result.Fail(creative.Id));
                            }
                        }
                    });
            }
        }
        return result;
    }

    public async Task<Result> UpdateCreativeTemplateNameReferenceAsync (CreativeTemplateId id,
        string creativeTemplateName, long orgId)
    {
        var creativesToUpdate = new List<Entities.Creative>();
        var partitionKey = new PartitionKey(orgId.ToString());
        IQueryable<Entities.Creative> creativeDbQuery = _container
            .GetItemLinqQueryable<Entities.Creative>(
                true,
                requestOptions: new QueryRequestOptions { PartitionKey = partitionKey },
                linqSerializerOptions: new CosmosLinqSerializerOptions()
                {
                    PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase
                })
            .Where(c => c.CreativeTemplateId == id.ToString());

        using (var resultSet = creativeDbQuery.ToFeedIterator())
        {
            while (resultSet.HasMoreResults)
            {
                FeedResponse<Entities.Creative>? response = await _retryPolicy.ExecuteAsync(() => resultSet.ReadNextAsync());
                creativesToUpdate.AddRange(response.Resource);
            }
        }

        foreach (Entities.Creative creative in creativesToUpdate)
        {
            creative.CreativeTemplateName = creativeTemplateName;
            await _container.UpsertItemAsync(creative, partitionKey);
        }

        return Result.Ok();
    }
}
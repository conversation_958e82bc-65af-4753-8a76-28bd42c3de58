﻿using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Factory;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.CreativeFileSanitizers;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using FluentResults;
using MediatR;
using Fattail.CreativeManagement.API.Domain.Common;

namespace Fattail.CreativeManagement.API.Application.Creatives.CreativeAdd;

public class CreativeAddHandler (
    IIdManager idManager,
    IDateTimeProvider dateTimeProvider,
    ICreativeTemplateRepository creativeTemplateRepository,
    ICreativeRepository creativeRepository,
    ICreativeFactory creativeFactory,
    ICreativeFilesVerifier creativeFilesVerifier)
    : IRequestHandler<CreativeAddCommand, Result<CreativeResult>>
{
    public async Task<Result<CreativeResult>> Handle (CreativeAddCommand request,
        CancellationToken cancellationToken)
    {
        CreativeTemplate? creativeTemplate = request.CreativeTemplateId > 0
            ? await creativeTemplateRepository.FindByIdAsync(new CreativeTemplateId(request.CreativeTemplateId))
            : null;

        var fields = request.Fields.ToDictionary(x => new CreativeFieldId(x.Id), v => v.GetValue());

        var allCreativeFileIds = ExtractCreativeFileIds(request.Fields);
        var resolvedCreativeFiles = allCreativeFileIds?.Any() == true
            ? await creativeFilesVerifier.GetExistingCreativeFilesFrom(allCreativeFileIds.ToArray())
            : new List<CreativeFile>();
        var creativeFilesLookup = (resolvedCreativeFiles ?? new List<CreativeFile>()).ToDictionary(cf => cf.Id, cf => cf);

        Result<Creative> creativeResult = await creativeFactory.Create(new CreateCreativeRequest(
            idManager.GetId(),
            request.Name,
            creativeTemplate,
            request.AdBookClientId,
            null,
            request.CampaignId,
            request.LineItemIds,
            request.UpdatedBy,
            fields,
            dateTimeProvider.CurrentTime
            ), creativeFilesLookup);

        if (creativeResult.IsFailed)
        {
            return creativeResult.ToResult();
        }

        Creative creative = creativeResult.Value;

        return await creativeRepository.CreateAsync<CreativeResult>(creative);
    }

    private static IEnumerable<CreativeFileId> ExtractCreativeFileIds (IEnumerable<CreativeFieldDto> fields)
    {
        return fields
            .Where(f => f.Type == CreativeFieldType.FileUpload || f.Type == CreativeFieldType.MultiFileUpload)
            .SelectMany(f => f switch
            {
                FileUploadFieldValue fileUpload => new[] { new CreativeFileId(fileUpload.Value) },
                MultiFileUploadFieldValue multiFileUpload =>
                    multiFileUpload.Value.Select(id => new CreativeFileId(id)),
                _ => Enumerable.Empty<CreativeFileId>()
            });
    }
}
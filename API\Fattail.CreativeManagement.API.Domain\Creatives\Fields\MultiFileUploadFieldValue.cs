﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Guards;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields;

public sealed class MultiFileUploadFieldValue : CreativeFieldValue<IReadOnlyList<CreativeFileId>>
{
    private MultiFileUploadFieldValue (CreativeFieldIdentifier creativeFieldIdentifier,
        IEnumerable<CreativeFileId>? value)
        : base(creativeFieldIdentifier, value?.ToList() ?? new List<CreativeFileId>())
    {
    }

    internal static async Task<Result<MultiFileUploadFieldValue>> Create (
        CreativeFieldIdentifier creativeFieldIdentifier, object? value, IEnumerable<ValidationRule> validationRules,
        IReadOnlyDictionary<CreativeFileId, CreativeFile>? creativeFiles = null)
    {
        Guard.Argument(creativeFieldIdentifier, nameof(creativeFieldIdentifier))
            .FieldTypeIs(CreativeFieldType.MultiFileUpload);

        IReadOnlyList<CreativeFile>? creativeFilesForValidation = null;
        IEnumerable<CreativeFileId>? creativeFileIds = null;

        if (value is not null)
        {
            Guard.Argument(value, nameof(value)).Compatible<IEnumerable<CreativeFileId>>();

            var fileIds = (IEnumerable<CreativeFileId>)value;
            creativeFileIds = fileIds;

            if (creativeFiles != null)
            {
                var resolvedFiles = fileIds
                    .Where(id => creativeFiles.ContainsKey(id))
                    .Select(id => creativeFiles[id])
                    .ToList();

                var missingFiles = fileIds.Where(id => !creativeFiles.ContainsKey(id)).ToList();
                if (missingFiles.Any())
                {
                    return Result.Fail($"CreativeFiles with IDs {string.Join(", ", missingFiles)} not found");
                }

                if (resolvedFiles.Any())
                {
                    creativeFilesForValidation = resolvedFiles;
                }
            }
            else
            {
                // CreativeFiles dictionary was not provided, cannot validate files
                return Result.Fail("CreativeFiles dictionary is required for file validation");
            }
        }

        Result validationResults = await CreativeFieldValueValidationManager.Validate(creativeFilesForValidation, validationRules,
            creativeFieldIdentifier);

        return validationResults.IsFailed
            ? validationResults
            : new MultiFileUploadFieldValue(creativeFieldIdentifier, creativeFileIds);
    }

    public override async Task<Result<CreativeFieldValue>> GenerateNewValue (object? value,
        IEnumerable<ValidationRule> validationRules,
        IReadOnlyDictionary<CreativeFileId, CreativeFile>? creativeFiles = null)
    {
        return (await Create(CreativeFieldIdentifier, value, validationRules, creativeFiles))
            .ToResult<CreativeFieldValue>(creativeFieldValue => creativeFieldValue);
    }
}
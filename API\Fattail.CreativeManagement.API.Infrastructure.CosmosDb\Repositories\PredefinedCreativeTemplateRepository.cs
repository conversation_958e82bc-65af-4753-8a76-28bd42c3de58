﻿using AutoMapper;
using Fattail.CreativeManagement.API.Domain.Repositories;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Interfaces;
using Microsoft.Azure.Cosmos;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Settings;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Entities.Enums;

namespace Fattail.CreativeManagement.API.Infrastructure.CosmosDb.Repositories;

internal sealed class PredefinedCreativeTemplateRepository (
    ICosmosDbContainerFactory cosmosDbContainerFactory,
    IMapper mapper,
    IOrganizationContext organizationContext,
    IOptions<ParallelExecutionSettings> parallelExecutionSettings,
    ILogger<PredefinedCreativeTemplateRepository> log)
    :
        CosmosDbRepository<CreativeTemplate, CreativeTemplateId, Entities.CreativeTemplate>(cosmosDbContainerFactory,
            mapper,
            organizationContext, parallelExecutionSettings, log),
        IPredefinedCreativeTemplateRepository
{
    public override string ContainerName => "CreativeTemplates";

    public override PartitionKey ResolvePartitionKey ()
    {
        return new PartitionKey(PredefinedPartitions.Shared.ToString());
    }

    protected override async Task<ItemResponse<Entities.CreativeTemplate>> CreateEntityAsync (CreativeTemplate entity)
    {
        Entities.CreativeTemplate cosmosEntity = PrepareCosmosEntity(entity, ActionType.Create);
        cosmosEntity.OrgId = PredefinedPartitions.Shared.ToString();
        return await _container.CreateItemAsync(cosmosEntity, ResolvePartitionKey());
    }
}
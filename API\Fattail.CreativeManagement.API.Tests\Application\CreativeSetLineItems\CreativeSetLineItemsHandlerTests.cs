﻿using AutoBogus;
using Bogus;
using Fattail.CreativeManagement.API.Application;
using Fattail.CreativeManagement.API.Application.Creatives;
using Fattail.CreativeManagement.API.Application.Creatives.CreativeSetLineItems;
using Fattail.CreativeManagement.API.Domain.Common;
using Fattail.CreativeManagement.API.Domain.Common.Errors;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.Creatives;
using Fattail.CreativeManagement.API.Domain.Creatives.Factory;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Sanitizers.StrategyProvider;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Factory;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Requirements;
using Fattail.CreativeManagement.API.Domain.Repositories;
using FluentAssertions;
using FluentResults;
using FluentResults.Extensions.FluentAssertions;
using Moq;
using NUnit.Framework;

namespace Fattail.CreativeManagement.API.Tests.Application.CreativeSetLineItems;

[TestFixture]
public class CreativeSetLineItemsHandlerTests
{
    [SetUp]
    public void SetUp ()
    {
        _idManagerMock = new Mock<IIdManager>();
        _idManagerMock.SetupSequence(idManager => idManager.GetId()).Returns(123).Returns(456);

        _dateTimeProviderMock = new Mock<IDateTimeProvider>();
        _creativeRepositoryMock = new Mock<ICreativeRepository>();
        _adBookWorkflowHttpClient = new Mock<IAdBookWorkflowHttpClient>();
        _sanitizerStrategyProviderMock = new Mock<ISanitizerStrategyProvider>();

        _sanitizerStrategyProviderMock = new Mock<ISanitizerStrategyProvider>();
        _sanitizerMock = new Mock<ISanitizer>();

        _sanitizerStrategyProviderMock
            .Setup(sanitizerStrategyProvider =>
                sanitizerStrategyProvider.GetFrom(CreativeFieldType.MultiFileUpload))
            .Returns(_sanitizerMock.Object);

        _creativeFilesManagerMock = new Mock<ICreativeFilesManager>();

        _creativeFactory = new CreativeFactory();

        _creativeSetLineItemsHandler = new CreativeSetLineItemsHandler(
            _creativeRepositoryMock.Object,
            _dateTimeProviderMock.Object,
            _adBookWorkflowHttpClient.Object);
    }

    private CreativeSetLineItemsHandler _creativeSetLineItemsHandler;
    private Mock<IDateTimeProvider> _dateTimeProviderMock = null!;
    private Mock<ICreativeRepository> _creativeRepositoryMock = null!;
    private Mock<IAdBookWorkflowHttpClient> _adBookWorkflowHttpClient = null!;
    private Mock<ISanitizerStrategyProvider> _sanitizerStrategyProviderMock = null!;
    private Mock<ISanitizer> _sanitizerMock = null!;
    private Mock<IIdManager> _idManagerMock = null!;
    private Mock<ICreativeFilesManager> _creativeFilesManagerMock = null!;
    private ICreativeFactory _creativeFactory = null!;
    private readonly string _fieldName = "fieldName";

    [Test]
    public async Task Valid_line_items_are_assigned_to_creative ()
    {
        Creative creative = CreateCreative();
        CreativeSetLineItemsCommand command = new AutoFaker<CreativeSetLineItemsCommand>()
            .RuleFor(creativeEditCommand => creativeEditCommand.Id, faker => faker.Random.Long(1))
            .RuleFor(creativeEditCommand => creativeEditCommand.LineItemIds, faker => Enumerable.Range(1, 5)
                .Select(_ => faker.Random.Long())
                .ToHashSet())
            .Generate();

        _creativeRepositoryMock.Setup(x => x.FindByIdAsync(command.Id)).ReturnsAsync(creative);

        Result<CreativeResult> result = await _creativeSetLineItemsHandler.Handle(command, CancellationToken.None);

        result.Should().BeSuccess();
        _creativeRepositoryMock.Verify(x => x.UpdateAsync<CreativeResult>(creative), Times.Once);
    }

    [Test]
    public async Task Valid_line_items_assignment_fails_no_adBook_workflow_notification_is_triggered ()
    {
        Creative creative = CreateCreative();
        CreativeSetLineItemsCommand command = new AutoFaker<CreativeSetLineItemsCommand>()
            .RuleFor(creativeEditCommand => creativeEditCommand.Id, faker => faker.Random.Long(1))
            .RuleFor(creativeEditCommand => creativeEditCommand.LineItemIds, faker => Enumerable.Range(1, 5)
                .Select(_ => faker.Random.Long())
                .ToHashSet())
            .Generate();

        _creativeRepositoryMock.Setup(x => x.FindByIdAsync(command.Id)).ReturnsAsync(creative);
        _creativeRepositoryMock.Setup(x => x.UpdateAsync<CreativeResult>(creative)).Throws(new Exception());

        try
        {
            Result<CreativeResult> result = await _creativeSetLineItemsHandler.Handle(command, CancellationToken.None);
        }
        catch (Exception e)
        {
            e.Should().BeOfType<Exception>();
            e.Message.Should().NotBeNullOrEmpty();
            _creativeRepositoryMock.Verify(x => x.UpdateAsync<CreativeResult>(creative), Times.Once);
            _adBookWorkflowHttpClient.Verify(x => x.NotifyLineItemAssignment(
                creative.Id,
                creative.CampaignId,
                command.LineItemIds), Times.Never);
        }
    }

    [Test]
    public async Task Valid_line_items_are_assigned_and_workflow_notification_is_triggered ()
    {
        Creative creative = CreateCreative();
        CreativeSetLineItemsCommand command = new AutoFaker<CreativeSetLineItemsCommand>()
            .RuleFor(creativeEditCommand => creativeEditCommand.Id, faker => faker.Random.Long(1))
            .RuleFor(creativeEditCommand => creativeEditCommand.LineItemIds, faker => Enumerable.Range(1, 5)
                .Select(_ => faker.Random.Long(1000000))
                .ToHashSet())
            .Generate();

        _creativeRepositoryMock.Setup(x => x.FindByIdAsync(command.Id)).ReturnsAsync(creative);

        Result<CreativeResult> result = await _creativeSetLineItemsHandler.Handle(command, CancellationToken.None);

        result.Should().BeSuccess();
        _creativeRepositoryMock.Verify(x => x.UpdateAsync<CreativeResult>(creative), Times.Once);
        _adBookWorkflowHttpClient.Verify(x => x.NotifyLineItemAssignment(
            creative.Id,
            creative.CampaignId,
            command.LineItemIds), Times.Once);
    }

    [Test]
    public async Task Null_line_items_returns_required_value_missing_error ()
    {
        Creative creative = CreateCreative();
        CreativeSetLineItemsCommand command = new AutoFaker<CreativeSetLineItemsCommand>()
            .RuleFor(creativeEditCommand => creativeEditCommand.Id, faker => faker.Random.Long(1))
            .RuleFor(creativeEditCommand => creativeEditCommand.LineItemIds, () => null)
            .Generate();

        _creativeRepositoryMock.Setup(x => x.FindByIdAsync(command.Id)).ReturnsAsync(creative);

        Result<CreativeResult> result = await _creativeSetLineItemsHandler.Handle(command, CancellationToken.None);

        result.Should().BeFailure();
        result.Should().HaveReason(new RequiredValueMissingError("lineItemIds", nameof(Creative)));
        _creativeRepositoryMock.Verify(x => x.UpdateAsync<CreativeResult>(creative), Times.Never);
        _adBookWorkflowHttpClient.Verify(x => x.NotifyLineItemAssignment(
            creative.Id,
            creative.CampaignId,
            It.IsAny<IEnumerable<long>>()), Times.Never);
    }

    private Creative CreateCreative ()
    {
        var faker = new Faker();

        DateTime lastUpdatedOn = DateTime.UtcNow;
        _dateTimeProviderMock.SetupGet(dateTimeProvider => dateTimeProvider.CurrentTime).Returns(lastUpdatedOn);

        var uniqueNameRequirement = new CreativeFieldUniqueNameRequirement(true);

        var createCreativeTemplateRequest = new CreativeTemplateCreateRequest(
            new CreativeTemplateId(123),
            CreativeTemplateName.Create("creative template name", new CreativeTemplateUniqueNameRequirement(true)).Value,
            CreativeType.Undefined,
            [new(new(123), 1), new(new(456), 2)],
            new HashSet<CreativeField>([
                CreativeField.Create(new CreativeFieldId(123), _fieldName, CreativeFieldType.SingleLineText, uniqueNameRequirement, false, null).Value,
                CreativeField.Create(new CreativeFieldId(456), _fieldName, CreativeFieldType.MultiFileUpload, uniqueNameRequirement, false, null).Value
            ]),
            false
        );

        CreativeTemplate creativeTemplate = CreativeTemplateFactory.Create(createCreativeTemplateRequest).Value;

        string creativeName = faker.Random.String();
        long adBookClientId = faker.Random.Long(1);
        long campaignId = faker.Random.Long(1);
        var lineItems = new List<long> { 123, 456, 3 };
        string updater = faker.Random.String();

        Result<Creative> creativeResult = _creativeFactory.Create(new CreateCreativeRequest(
            _idManagerMock.Object.GetId(),
            creativeName,
            creativeTemplate,
            adBookClientId,
            null,
            campaignId,
            lineItems.ToHashSet(),
            updater,
            new Dictionary<CreativeFieldId, object?>(),
            _dateTimeProviderMock.Object.CurrentTime
        )).Result;

        return creativeResult.Value;
    }
}
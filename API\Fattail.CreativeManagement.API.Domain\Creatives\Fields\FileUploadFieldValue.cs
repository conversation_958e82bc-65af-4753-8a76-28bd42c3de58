﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Guards;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields;

public sealed class FileUploadFieldValue : CreativeFieldValue<CreativeFileId>
{
    public FileUploadFieldValue (CreativeFieldIdentifier creativeFieldIdentifier, CreativeFileId? value) : base(
        creativeFieldIdentifier, value)
    {
    }

    internal static Result<FileUploadFieldValue> Create (
        CreativeFieldIdentifier creativeFieldIdentifier, object? value, IEnumerable<ValidationRule> validationRules,
        IReadOnlyDictionary<CreativeFileId, CreativeFile>? creativeFiles = null)
    {
        Guard.Argument(creativeFieldIdentifier, nameof(creativeFieldIdentifier))
            .FieldTypeIs(CreativeFieldType.FileUpload);

        IReadOnlyList<CreativeFile?> creativeFilesForValidation = null;
        CreativeFile? creativeFile = null;
        CreativeFileId? creativeFileId = null;

        if (value is not null)
        {
            Guard.Argument(value, nameof(value)).Compatible<CreativeFileId>();

            creativeFileId = (CreativeFileId)value;

            if (creativeFiles?.TryGetValue(creativeFileId, out creativeFile) == true)
            {
                creativeFilesForValidation = new List<CreativeFile> { creativeFile };
            }
            else if (creativeFiles is not null)
            {
                // CreativeFiles dictionary was provided but the file was not found
                return Result.Fail($"CreativeFile with ID {creativeFileId} not found");
            }
            else
            {
                // CreativeFiles dictionary was not provided, cannot validate file
                return Result.Fail("CreativeFiles dictionary is required for file validation");
            }
        }

        Result validationResults = CreativeFieldValueValidationManager.Validate(creativeFilesForValidation, validationRules,
            creativeFieldIdentifier);

        return validationResults.IsFailed
            ? validationResults
            : new FileUploadFieldValue(creativeFieldIdentifier, creativeFileId);
    }

    public override Result<CreativeFieldValue> GenerateNewValue (object? value,
        IEnumerable<ValidationRule> validationRules,
        IReadOnlyDictionary<CreativeFileId, CreativeFile>? creativeFiles = null)
    {
        return Create(CreativeFieldIdentifier, value, validationRules, creativeFiles)
            .ToResult<CreativeFieldValue>(creativeFieldValue => creativeFieldValue);
    }
}
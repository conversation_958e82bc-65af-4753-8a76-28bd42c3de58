﻿using Dawn;
using Fattail.CreativeManagement.API.Domain.CreativeFields;
using Fattail.CreativeManagement.API.Domain.CreativeFiles;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Guards;
using Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators;
using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields;

public sealed class SingleLineTextFieldValue : CreativeFieldValue<string>
{
    public SingleLineTextFieldValue (CreativeFieldIdentifier creativeFieldIdentifier, string? value) : base(
        creativeFieldIdentifier, value)
    {
    }

    internal static Result<SingleLineTextFieldValue> Create (
        CreativeFieldIdentifier creativeFieldIdentifier, object? value, IEnumerable<ValidationRule> validationRules)
    {
        Guard.Argument(creativeFieldIdentifier, nameof(creativeFieldIdentifier))
            .FieldTypeIs(CreativeFieldType.SingleLineText);

        if (value is not null)
        {
            Guard.Argument(value, nameof(value)).Compatible<string>();
        }

        Result validationResults =
            CreativeFieldValueValidationManager.Validate(value?.ToString(), validationRules,
                creativeFieldIdentifier);

        return validationResults.IsFailed
            ? validationResults
            : new SingleLineTextFieldValue(creativeFieldIdentifier, value?.ToString());
    }


    public override Result<CreativeFieldValue> GenerateNewValue (object? value,
        IEnumerable<ValidationRule> validationRules,
        IReadOnlyDictionary<CreativeFileId, CreativeFile>? creativeFiles = null)
    {
        return Create(CreativeFieldIdentifier, value, validationRules)
            .ToResult<CreativeFieldValue>(creativeFieldValue => creativeFieldValue);
    }
}
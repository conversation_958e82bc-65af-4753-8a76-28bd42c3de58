﻿using Fattail.CreativeManagement.API.Domain.CreativeTemplates.Fields;
using FluentResults;

namespace Fattail.CreativeManagement.API.Domain.Creatives.Fields.Validators
{
    public static class CreativeFieldValueValidationManager
    {
        public static Result Validate<TFieldValue> (TFieldValue values, IEnumerable<ValidationRule> validationRules, CreativeFieldIdentifier creativeFieldIdentifier)
        {
            var result = Result.Ok();
            IReadOnlyList<IRuleValidator<TFieldValue>> validators = CreativeFieldValidatorsFactory.GetValidators<TFieldValue>(validationRules, creativeFieldIdentifier);

            foreach (IRuleValidator<TFieldValue> validator in validators)
            {
                Result validationResult = validator.IsValid(values);
                result = Result.Merge(result, validationResult);
            }

            return result;
        }
    }
}
